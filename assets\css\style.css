/* ===== 全局样式 ===== */
:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --bg-light: #f8fafc;
    --white: #ffffff;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* ===== 按钮样式 ===== */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #1e40af;
    transform: translateY(-2px);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-outline-light {
    border: 2px solid var(--white);
    color: var(--white);
    background: transparent;
}

.btn-outline-light:hover {
    background-color: var(--white);
    color: var(--primary-color);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

/* ===== 头部样式 ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--white);
    box-shadow: var(--shadow);
}

.top-bar {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 8px 0;
    font-size: 14px;
}

.contact-info span {
    margin-right: 20px;
}

.contact-info i {
    margin-right: 5px;
}

.top-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
}

.language-switcher {
    display: flex;
    gap: 10px;
}

.lang-btn {
    color: var(--white);
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.lang-btn:hover,
.lang-btn.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.social-links a {
    color: var(--white);
    margin-left: 10px;
    font-size: 16px;
    transition: color 0.3s;
}

.social-links a:hover {
    color: var(--accent-color);
}

/* ===== 导航栏样式 ===== */
.navbar {
    padding: 15px 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-dark);
}

.logo {
    width: 60px;
    height: 60px;
    margin-right: 15px;
}

.brand-text h1 {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: var(--primary-color);
}

.brand-text span {
    font-size: 14px;
    color: var(--text-light);
}

.navbar-nav .nav-link {
    color: var(--text-dark);
    font-weight: 500;
    padding: 10px 15px;
    transition: color 0.3s;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 8px;
    padding: 10px 0;
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 200px;
    background-color: var(--primary-color);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    padding: 8px 20px;
    transition: all 0.3s;
    color: var(--white);
    text-decoration: none;
    display: block;
    white-space: nowrap;
    border: none;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.dropdown-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--white);
}

/* ===== 英雄区域样式 ===== */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
    margin-top: 100px;
}

.hero-slider {
    position: relative;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    filter: brightness(1.1) contrast(1.05);
}

.hero-slide.active {
    opacity: 1;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(30, 58, 138, 0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    color: var(--white);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.hero-prev {
    left: 30px;
}

.hero-next {
    right: 30px;
}

.hero-prev,
.hero-next {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.hero-prev:hover,
.hero-next:hover {
    background: rgba(255, 255, 255, 0.3);
}

.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s;
}

.indicator.active {
    background: var(--white);
}

/* ===== 快速链接样式 ===== */
.quick-links {
    padding: 80px 0;
    background: var(--bg-light);
}

.quick-link-item {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.quick-link-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.quick-link-item .icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 32px;
}

.quick-link-item h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.quick-link-item p {
    color: var(--text-light);
    margin-bottom: 20px;
}

/* ===== 统计数据样式 ===== */
.statistics-section {
    padding: 80px 0;
    background: var(--primary-color);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-blend-mode: overlay;
    color: var(--white);
    position: relative;
}

.statistics-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(100, 124, 191, 0.2);
    z-index: 1;
}

.statistics-section .container {
    position: relative;
    z-index: 2;
}

.stat-item {
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--accent-color);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* ===== 新闻区域样式 ===== */
.news-section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-light);
}

.news-grid {
    display: grid;
    gap: 30px;
    margin-bottom: 40px;
}

.news-item {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.news-item.featured {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
}

.news-image {
    position: relative;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s;
}

.news-item:hover .news-image img {
    transform: scale(1.05);
}

.news-date {
    position: absolute;
    top: 20px;
    left: 20px;
    background: var(--primary-color);
    color: var(--white);
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    min-width: 60px;
}

.news-date .day {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
}

.news-date .month {
    display: block;
    font-size: 0.9rem;
}

.news-content {
    padding: 30px;
}

.news-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.news-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.news-content h3 a,
.news-content h4 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s;
}

.news-content h3 a:hover,
.news-content h4 a:hover {
    color: var(--primary-color);
}

.news-content p {
    color: var(--text-light);
    margin-bottom: 15px;
}

.news-meta {
    display: flex;
    gap: 20px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.news-date-small {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* ===== 侧边栏样式 ===== */
.sidebar {
    padding-left: 30px;
}

.widget {
    background: var(--white);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: var(--shadow);
}

.widget h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.announcement-list {
    list-style: none;
    margin-bottom: 20px;
}

.announcement-list li {
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.announcement-list li:last-child {
    border-bottom: none;
}

.announcement-list a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    flex: 1;
    transition: color 0.3s;
}

.announcement-list a:hover {
    color: var(--primary-color);
}

.announcement-list .date {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-left: 10px;
}

.quick-links-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.quick-link-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 15px;
    background: var(--bg-light);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s;
}

.quick-link-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.quick-link-btn i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.quick-link-btn span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== 页脚样式 ===== */
.footer {
    background: var(--text-dark);
    color: var(--white);
}

.footer-main {
    padding: 60px 0 40px;
}

.footer-widget h3,
.footer-widget h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--white);
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
}

.footer-widget p {
    color: #d1d5db;
    margin-bottom: 20px;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--accent-color);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.contact-item i {
    color: var(--accent-color);
    margin-right: 10px;
    margin-top: 2px;
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding: 20px 0;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}

.footer-bottom-links a {
    color: #d1d5db;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s;
}

.footer-bottom-links a:hover {
    color: var(--accent-color);
}

/* ===== 返回顶部按钮 ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* ===== 页面头部样式 ===== */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 150px 0 80px;
    margin-top: 100px;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--white);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: rgba(255, 255, 255, 0.6);
}

/* ===== 内容页面样式 ===== */
.about-content {
    padding: 80px 0;
}

.content-wrapper {
    background: var(--white);
    padding: 40px;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.content-wrapper h2 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    border-bottom: 3px solid var(--accent-color);
    padding-bottom: 15px;
}

.content-wrapper h3 {
    color: var(--text-dark);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 30px 0 15px;
}

.content-wrapper .lead {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 25px;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: 30px;
}

.feature-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 15px;
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list li strong {
    color: var(--primary-color);
    font-weight: 600;
}

.feature-list li:hover {
    background-color: var(--bg-light);
    padding-left: 35px;
    transition: all 0.3s ease;
}

.quick-facts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.fact-item {
    text-align: center;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
}

.fact-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.fact-label {
    font-size: 0.9rem;
    color: var(--text-light);
}

.link-list {
    list-style: none;
    padding: 0;
}

.link-list li {
    margin-bottom: 10px;
}

.link-list a {
    color: var(--text-dark);
    text-decoration: none;
    padding: 8px 0;
    display: block;
    border-bottom: 1px solid transparent;
    transition: all 0.3s;
}

.link-list a:hover {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    padding-left: 10px;
}

/* ===== 新闻页面样式 ===== */
.news-content {
    padding: 80px 0;
}

.news-filter {
    margin-bottom: 40px;
}

.filter-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid var(--border-color);
    background: var(--white);
    color: var(--text-dark);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.news-list {
    display: grid;
    gap: 30px;
}

.news-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 0;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.news-card .news-image {
    position: relative;
    overflow: hidden;
}

.news-card .news-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.news-card .news-content {
    padding: 25px;
}

.news-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.news-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 1.4;
}

.news-card h3 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s;
}

.news-card h3 a:hover {
    color: var(--primary-color);
}

.news-card p {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
}

.read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: gap 0.3s;
}

.read-more:hover {
    gap: 10px;
}

.pagination-wrapper {
    margin-top: 50px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    gap: 5px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.page-item {
    margin: 0;
}

.page-link {
    display: block;
    padding: 10px 15px;
    color: var(--text-dark);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    transition: all 0.3s;
}

.page-item.active .page-link {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.page-item.disabled .page-link {
    color: var(--text-light);
    cursor: not-allowed;
}

.page-link:hover:not(.disabled) {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.search-form {
    margin-bottom: 20px;
}

.search-input-group {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
}

.search-input-group input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    outline: none;
}

.search-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background: var(--secondary-color);
}

.popular-news {
    list-style: none;
    padding: 0;
}

.popular-news li {
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.popular-news li:last-child {
    border-bottom: none;
}

.popular-news a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    display: block;
    margin-bottom: 5px;
    transition: color 0.3s;
}

.popular-news a:hover {
    color: var(--primary-color);
}

.popular-news .views {
    font-size: 0.8rem;
    color: var(--text-light);
}

.news-archive {
    list-style: none;
    padding: 0;
}

.news-archive li {
    margin-bottom: 8px;
}

.news-archive a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s;
}

.news-archive a:hover {
    color: var(--primary-color);
}

.news-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    display: inline-block;
    padding: 5px 12px;
    background: var(--bg-light);
    color: var(--text-dark);
    text-decoration: none;
    border-radius: 15px;
    font-size: 0.8rem;
    transition: all 0.3s;
}

.tag:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* ===== 联系页面样式 ===== */
.contact-content {
    padding: 80px 0;
}

.contact-form-wrapper {
    background: var(--white);
    padding: 40px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    margin-bottom: 40px;
}

.contact-form-wrapper h2 {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.contact-form-wrapper p {
    color: var(--text-light);
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-control.error {
    border-color: #dc3545;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-check {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.form-check-input {
    margin-top: 4px;
}

.form-check-label {
    font-size: 0.9rem;
    color: var(--text-light);
}

.form-check-label a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-info-wrapper {
    background: var(--white);
    padding: 40px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    height: fit-content;
}

.contact-info-wrapper h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.contact-info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.contact-info-item .icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-info-item .content h4 {
    color: var(--text-dark);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.contact-info-item .content p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.6;
}

.social-media {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

.social-media h4 {
    color: var(--text-dark);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.social-media .social-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: var(--bg-light);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.2rem;
    width: 20px;
}

.map-section {
    padding: 80px 0;
    background: var(--bg-light);
}

.map-section h2 {
    text-align: center;
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 40px;
}

.map-wrapper {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.map-placeholder {
    height: 400px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-align: center;
}

.map-content i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--accent-color);
}

.map-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.map-content p {
    margin-bottom: 10px;
    opacity: 0.9;
}

.departments-section {
    padding: 80px 0;
}

.departments-section h2 {
    text-align: center;
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 50px;
}

.department-card {
    background: var(--white);
    padding: 30px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.department-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.department-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 2rem;
}

.department-card h3 {
    color: var(--text-dark);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.department-info {
    margin-bottom: 20px;
}

.department-info p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.department-info i {
    color: var(--primary-color);
    width: 16px;
}

.department-card > p {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* ===== 学院页面样式 ===== */
.faculties-overview {
    padding: 80px 0;
    background: var(--bg-light);
}

.section-intro {
    text-align: center;
    margin-bottom: 50px;
}

.section-intro h2 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.section-intro .lead {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
}

.faculty-stats-card {
    background: var(--white);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.faculty-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stats-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 2rem;
}

.stats-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stats-label {
    color: var(--text-light);
    font-size: 1.1rem;
}

.faculties-grid {
    padding: 80px 0;
}

.faculty-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    overflow: hidden;
}

.faculty-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.faculty-header {
    display: flex;
    align-items: center;
    padding: 30px;
    background: linear-gradient(135deg, var(--bg-light), var(--white));
    border-bottom: 1px solid var(--border-color);
}

.faculty-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    margin-right: 20px;
    flex-shrink: 0;
}

.faculty-icon.engineering {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.faculty-icon.business {
    background: linear-gradient(135deg, #10b981, #059669);
}

.faculty-icon.humanities {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.faculty-icon.science {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.faculty-icon.medicine {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.faculty-icon.arts {
    background: linear-gradient(135deg, #ec4899, #db2777);
}

.faculty-info h3 {
    color: var(--text-dark);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.faculty-info p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

.faculty-content {
    padding: 30px;
}

.faculty-content > p {
    color: var(--text-light);
    margin-bottom: 25px;
    line-height: 1.6;
}

.faculty-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
}

.faculty-stats .stat-item {
    text-align: center;
}

.faculty-stats .number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.faculty-stats .label {
    font-size: 0.9rem;
    color: var(--text-light);
}

.faculty-programs h4 {
    color: var(--text-dark);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.faculty-programs ul {
    list-style: none;
    padding: 0;
    margin-bottom: 25px;
}

.faculty-programs li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: 20px;
}

.faculty-programs li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.faculty-programs li:last-child {
    border-bottom: none;
}

/* ===== 招生页面样式 ===== */
.admission-hero {
    padding: 80px 0;
    background: var(--bg-light);
}

.admission-content h2 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.admission-highlights {
    margin: 30px 0;
}

.highlight-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.highlight-item i {
    color: var(--accent-color);
    margin-right: 10px;
    font-size: 1.2rem;
}

.admission-actions {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.admission-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    width: 100%;
    max-width: 400px;
    height: 300px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-align: center;
}

.image-placeholder i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: var(--accent-color);
}

.image-placeholder h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.important-dates {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 50px;
}

.date-card {
    background: var(--white);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.date-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.date-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 2rem;
}

.date-card h3 {
    color: var(--text-dark);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.date-card .date {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.date-card p {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.5;
}

.admission-requirements {
    padding: 80px 0;
    background: var(--bg-light);
}

.requirements-content {
    background: var(--white);
    padding: 40px;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.requirement-category {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.requirement-category:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.requirement-category h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.requirement-category ul {
    list-style: none;
    padding: 0;
}

.requirement-category li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: 25px;
}

.requirement-category li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

.requirement-category li:last-child {
    border-bottom: none;
}

.fee-table {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 20px;
}

.fee-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.fee-row:last-child {
    border-bottom: none;
}

.fee-item {
    color: var(--text-dark);
    font-weight: 500;
}

.fee-amount {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.1rem;
}

.popular-programs {
    list-style: none;
    padding: 0;
}

.popular-programs li {
    margin-bottom: 10px;
}

.popular-programs a {
    color: var(--text-dark);
    text-decoration: none;
    padding: 8px 0;
    display: block;
    border-bottom: 1px solid transparent;
    transition: all 0.3s;
}

.popular-programs a:hover {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    padding-left: 10px;
}

.application-process {
    padding: 80px 0;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 2rem;
    font-weight: 700;
}

.step-content h3 {
    color: var(--text-dark);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.step-content p {
    color: var(--text-light);
    line-height: 1.6;
}

.online-application {
    padding: 80px 0;
    background: var(--bg-light);
}

.application-form-wrapper {
    background: var(--white);
    padding: 40px;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.application-form-wrapper h2 {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-align: center;
}

.application-form-wrapper > p {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 30px;
}

.form-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-block {
    width: 100%;
    margin-bottom: 10px;
}

/* ===== 研究生页面样式 ===== */
.graduate-overview {
    padding: 80px 0;
    background: var(--bg-light);
}

.program-type-card {
    background: var(--white);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.program-type-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.program-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 2rem;
}

.program-icon.masters {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.program-icon.phd {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.program-icon.professional {
    background: linear-gradient(135deg, #10b981, #059669);
}

.program-type-card h3 {
    color: var(--text-dark);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.program-type-card > p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 25px;
}

.program-details {
    margin-bottom: 25px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    color: var(--text-light);
    font-size: 0.9rem;
}

.detail-item .value {
    color: var(--primary-color);
    font-weight: 600;
}

.masters-programs {
    padding: 80px 0;
}

.program-content h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
    margin-top: 30px;
}

.program-content h3:first-child {
    margin-top: 0;
}

.requirements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.requirement-item {
    background: var(--bg-light);
    padding: 25px;
    border-radius: 8px;
}

.requirement-item h4 {
    color: var(--text-dark);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.requirement-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.requirement-item li {
    padding: 5px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 20px;
}

.requirement-item li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.popular-programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.program-item {
    background: var(--white);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow);
}

.program-item h4 {
    color: var(--text-dark);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.program-item p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

.timeline {
    position: relative;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: 60px;
    top: 25px;
    width: 2px;
    height: 40px;
    background: var(--border-color);
}

.timeline-item:last-child:before {
    display: none;
}

.timeline-date {
    width: 80px;
    font-size: 0.8rem;
    color: var(--primary-color);
    font-weight: 600;
    text-align: center;
    background: var(--bg-light);
    padding: 5px;
    border-radius: 4px;
    margin-right: 20px;
}

.timeline-content {
    color: var(--text-dark);
    font-size: 0.9rem;
}

.phd-programs {
    padding: 80px 0;
    background: var(--bg-light);
}

.phd-content h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.research-areas {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.area-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    transition: transform 0.3s;
}

.area-item:hover {
    transform: translateY(-2px);
}

.area-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.area-item span {
    color: var(--text-dark);
    font-weight: 500;
}

.phd-requirements h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.requirement-box {
    background: var(--white);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.requirement-box h4 {
    color: var(--text-dark);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.requirement-box ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.requirement-box li {
    padding: 8px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 20px;
}

.requirement-box li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

.graduate-application {
    padding: 80px 0;
}

.form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-of-type {
    border-bottom: none;
}

.form-section h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}

/* ===== 科研页面样式 ===== */
.research-overview {
    padding: 80px 0;
    background: var(--bg-light);
}

.research-stats-card {
    background: var(--white);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.research-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.research-centers {
    padding: 80px 0;
}

.research-center-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    overflow: hidden;
}

.research-center-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.center-header {
    display: flex;
    align-items: center;
    padding: 30px;
    background: linear-gradient(135deg, var(--bg-light), var(--white));
    border-bottom: 1px solid var(--border-color);
}

.center-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    margin-right: 20px;
    flex-shrink: 0;
}

.center-icon.ai {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.center-icon.biomedical {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.center-icon.energy {
    background: linear-gradient(135deg, #10b981, #059669);
}

.center-icon.materials {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.center-info h3 {
    color: var(--text-dark);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.center-info p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

.center-content {
    padding: 30px;
}

.center-content > p {
    color: var(--text-light);
    margin-bottom: 25px;
    line-height: 1.6;
}

.research-areas {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 25px;
}

.area-tag {
    display: inline-block;
    padding: 5px 12px;
    background: var(--bg-light);
    color: var(--primary-color);
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--primary-color);
}

.center-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
}

.center-stats .stat {
    text-align: center;
}

.center-stats .number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.center-stats .label {
    font-size: 0.9rem;
    color: var(--text-light);
}

.research-achievements {
    padding: 80px 0;
    background: var(--bg-light);
}

.achievement-card {
    background: var(--white);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 2rem;
}

.achievement-card h3 {
    color: var(--text-dark);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.achievement-card p {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
}

.achievement-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.achievement-meta .date {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.achievement-meta .category {
    color: var(--text-light);
    font-size: 0.8rem;
    background: var(--bg-light);
    padding: 3px 8px;
    border-radius: 10px;
}

/* ===== 奖学金页面样式 ===== */
.scholarship-overview {
    padding: 80px 0;
    background: var(--bg-light);
}

.scholarship-stats-card {
    background: var(--white);
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.scholarship-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.scholarship-types {
    padding: 80px 0;
}

.scholarship-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    overflow: hidden;
}

.scholarship-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.scholarship-header {
    display: flex;
    align-items: center;
    padding: 30px;
    background: linear-gradient(135deg, var(--bg-light), var(--white));
    border-bottom: 1px solid var(--border-color);
}

.scholarship-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    margin-right: 20px;
    flex-shrink: 0;
}

.scholarship-icon.academic {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.scholarship-icon.need {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.scholarship-icon.international {
    background: linear-gradient(135deg, #10b981, #059669);
}

.scholarship-icon.research {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.scholarship-info h3 {
    color: var(--text-dark);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.scholarship-info p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

.scholarship-content {
    padding: 30px;
}

.scholarship-content > p {
    color: var(--text-light);
    margin-bottom: 25px;
    line-height: 1.6;
}

.scholarship-details {
    background: var(--bg-light);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 500;
}

.detail-row .value {
    color: var(--primary-color);
    font-weight: 600;
}

.requirements h4 {
    color: var(--text-dark);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.requirements ul {
    list-style: none;
    padding: 0;
    margin-bottom: 25px;
}

.requirements li {
    padding: 8px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 20px;
}

.requirements li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

.scholarship-application {
    padding: 80px 0;
    background: var(--bg-light);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1199px) {
    .sidebar {
        padding-left: 0;
        margin-top: 40px;
    }

    .contact-info-wrapper {
        margin-top: 40px;
    }
}

@media (max-width: 991px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .news-item.featured {
        grid-template-columns: 1fr;
    }

    .quick-links-grid {
        grid-template-columns: 1fr;
    }

    .news-card {
        grid-template-columns: 1fr;
    }

    .quick-facts {
        grid-template-columns: 1fr;
    }

    .filter-tabs {
        justify-content: center;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 767px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: flex-start;
    }

    .top-right {
        flex-direction: column;
        gap: 10px;
    }

    .contact-info {
        margin-bottom: 10px;
    }

    .contact-info span {
        display: block;
        margin-bottom: 5px;
        margin-right: 0;
    }

    .footer-bottom-links {
        justify-content: center;
        margin-top: 15px;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .page-header {
        padding: 120px 0 60px;
        margin-top: 80px;
    }

    .hero-section {
        margin-top: 80px;
        height: 80vh;
    }

    .navbar-brand .brand-text h1 {
        font-size: 1.2rem;
    }

    .navbar-brand .brand-text span {
        font-size: 12px;
    }

    .dropdown-menu {
        position: static;
        display: block;
        box-shadow: none;
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 5px;
        background-color: var(--primary-color);
    }

    .article-title {
        font-size: 1.8rem;
    }

    .article-meta {
        flex-direction: column;
        gap: 10px;
    }

    .article-footer {
        flex-direction: column;
        align-items: flex-start;
    }

    .related-item {
        flex-direction: column;
        text-align: center;
    }

    .related-item img {
        width: 100%;
        height: 150px;
        margin-bottom: 10px;
    }

/* ===== 申请页面样式 ===== */
.application-overview {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.application-type-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.application-type-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.application-type-card .card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.application-type-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.application-type-card ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    flex-grow: 1;
}

.application-type-card ul li {
    padding: 5px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 20px;
}

.application-type-card ul li:before {
    content: "•";
    color: var(--accent-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

.quick-application {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.quick-app-wrapper {
    background: white;
    border-radius: 12px;
    padding: 40px;
    color: var(--text-color);
}

.quick-app-wrapper h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
    text-align: center;
}

.quick-app-form .form-group {
    margin-bottom: 20px;
}

.quick-app-form label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 8px;
    display: block;
}

.quick-app-form .form-control {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.quick-app-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 144, 220, 0.25);
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.form-actions .btn {
    margin: 0 10px;
    min-width: 150px;
}

.application-steps {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.step-card {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
}

.step-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 20px;
}

.step-content h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.step-content p {
    color: var(--text-light);
    line-height: 1.6;
}

/* ===== 学生服务页面样式 ===== */
.services-overview {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.academic-services,
.life-support-services,
.career-services,
.international-services {
    padding: 80px 0;
}

.life-support-services {
    background-color: var(--bg-light);
}

.international-services {
    background-color: var(--bg-light);
}

.service-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.service-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 1.8rem;
}

.service-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.service-card ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    flex-grow: 1;
}

.service-card ul li {
    padding: 8px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.service-card ul li:before {
    content: "→";
    color: var(--accent-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

.service-card ul li:last-child {
    border-bottom: none;
}

.service-contact {
    margin-top: auto;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.service-contact p {
    margin-bottom: 5px;
    font-size: 14px;
    color: var(--text-light);
}

.service-contact strong {
    color: var(--primary-color);
}

.service-hours {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.hours-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: var(--shadow);
    height: 100%;
}

.hours-card h3 {
    color: var(--primary-color);
    margin-bottom: 25px;
    font-weight: 600;
    text-align: center;
}

.hours-list {
    margin-bottom: 30px;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.hours-item:last-child {
    border-bottom: none;
}

.service-name {
    font-weight: 500;
    color: var(--text-dark);
}

.service-time {
    color: var(--primary-color);
    font-weight: 600;
}

.emergency-contact {
    background-color: #fff5f5;
    border: 2px solid #fed7d7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.emergency-contact h4 {
    color: #e53e3e;
    margin-bottom: 15px;
    font-weight: 600;
}

.emergency-contact p {
    margin-bottom: 8px;
    color: #e53e3e;
    font-weight: 500;
}

.contact-cta {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    text-align: center;
}

.cta-content h2 {
    margin-bottom: 15px;
    font-weight: 700;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-buttons .btn {
    margin: 0 10px;
    min-width: 150px;
}

/* ===== 新闻详情页面样式 ===== */
.news-detail-content {
    padding: 80px 0;
}

.news-detail-article {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: var(--shadow);
    margin-bottom: 40px;
}

.article-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
}

.article-title {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 20px;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    color: var(--text-light);
    font-size: 14px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.meta-item i {
    color: var(--primary-color);
}

.article-image {
    margin: 30px 0;
    text-align: center;
}

.article-image img {
    border-radius: 12px;
    box-shadow: var(--shadow);
    max-height: 400px;
    object-fit: cover;
}

.article-content {
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-color);
}

.article-content .lead {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 30px;
    padding: 20px;
    background-color: var(--bg-light);
    border-left: 4px solid var(--primary-color);
    border-radius: 0 8px 8px 0;
}

.article-content h3 {
    color: var(--primary-color);
    font-weight: 600;
    margin: 30px 0 15px;
    font-size: 1.5rem;
}

.article-content ul {
    margin: 20px 0;
    padding-left: 0;
}

.article-content ul li {
    list-style: none;
    padding: 10px 0;
    padding-left: 30px;
    position: relative;
    border-bottom: 1px solid #f0f0f0;
}

.article-content ul li:before {
    content: "▶";
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-size: 12px;
}

.article-content ul li:last-child {
    border-bottom: none;
}

.article-content ul li strong {
    color: var(--primary-color);
    font-weight: 600;
}

.article-content blockquote {
    background-color: var(--bg-light);
    border-left: 4px solid var(--secondary-color);
    padding: 20px;
    margin: 30px 0;
    border-radius: 0 8px 8px 0;
}

.article-content blockquote p {
    font-style: italic;
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.article-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.article-tags {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.tag-label {
    font-weight: 600;
    color: var(--text-dark);
}

.tag {
    background-color: var(--bg-light);
    color: var(--primary-color);
    padding: 5px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.tag:hover {
    background-color: var(--primary-color);
    color: white;
}

.article-share {
    display: flex;
    align-items: center;
    gap: 10px;
}

.share-label {
    font-weight: 600;
    color: var(--text-dark);
}

.share-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: var(--bg-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.share-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.related-news {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: var(--shadow);
}

.related-news h3 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 25px;
    font-size: 1.5rem;
}

.related-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.related-item:last-child {
    border-bottom: none;
}

.related-item img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    flex-shrink: 0;
}

.related-content h4 {
    font-size: 16px;
    margin-bottom: 8px;
}

.related-content h4 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.related-content h4 a:hover {
    color: var(--primary-color);
}

.related-date {
    font-size: 14px;
    color: var(--text-light);
}

.latest-news-item {
    display: flex;
    gap: 12px;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.latest-news-item:last-child {
    border-bottom: none;
}

.news-image-small {
    flex-shrink: 0;
}

.news-image-small img {
    width: 60px;
    height: 45px;
    object-fit: cover;
    border-radius: 6px;
}

.news-content-small h4 {
    font-size: 14px;
    margin-bottom: 5px;
    line-height: 1.4;
}

.news-content-small h4 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.news-content-small h4 a:hover {
    color: var(--primary-color);
}

.news-date-small {
    font-size: 12px;
    color: var(--text-light);
}

.category-list,
.archive-list {
    list-style: none;
    padding: 0;
}

.category-list li,
.archive-list li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.category-list li:last-child,
.archive-list li:last-child {
    border-bottom: none;
}

.category-list a,
.archive-list a {
    color: var(--text-dark);
    text-decoration: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: color 0.3s ease;
}

.category-list a:hover,
.archive-list a:hover {
    color: var(--primary-color);
}

.category-list span,
.archive-list span {
    background-color: var(--bg-light);
    color: var(--text-light);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

    .content-wrapper,
    .contact-form-wrapper,
    .contact-info-wrapper {
        padding: 25px;
    }

    .contact-info-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .social-media .social-links {
        align-items: center;
    }

    .filter-tabs {
        flex-direction: column;
        align-items: center;
    }

    .filter-btn {
        width: 200px;
        text-align: center;
    }
}

@media (max-width: 575px) {
    .hero-section {
        margin-top: 140px;
    }

    .hero-prev,
    .hero-next {
        width: 40px;
        height: 40px;
    }

    .hero-prev {
        left: 15px;
    }

    .hero-next {
        right: 15px;
    }

    .quick-link-item {
        padding: 30px 20px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .page-header {
        padding: 120px 0 60px;
    }

    .page-header h1 {
        font-size: 1.8rem;
    }

    .content-wrapper,
    .contact-form-wrapper,
    .contact-info-wrapper {
        padding: 20px;
    }

    .department-card {
        padding: 25px 20px;
    }

    .department-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .map-placeholder {
        height: 300px;
    }

    .map-content h3 {
        font-size: 1.5rem;
    }

    .news-meta {
        flex-direction: column;
        gap: 8px;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* ===== 认证资质页面样式 ===== */
.accreditation-overview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.accreditation-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.accreditation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.accreditation-card .card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
}

.accreditation-card .card-icon i {
    font-size: 2rem;
    color: white;
}

.accreditation-card h4 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 600;
}

.accreditation-details {
    list-style: none;
    padding: 0;
    margin-top: 20px;
}

.accreditation-details li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    color: var(--text-muted);
}

.accreditation-details li:last-child {
    border-bottom: none;
}

.professional-cert-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.professional-cert-card:hover {
    transform: translateY(-3px);
}

.cert-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 25px 20px;
    text-align: center;
}

.cert-header i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.cert-header h5 {
    margin: 0;
    font-weight: 600;
}

.cert-body {
    padding: 25px 20px;
    text-align: center;
}

.cert-badge {
    display: inline-block;
    background: var(--accent-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-top: 15px;
}

.quality-timeline {
    position: relative;
    padding: 20px 0;
}

.quality-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
    padding-left: 80px;
}

.timeline-marker {
    position: absolute;
    left: 15px;
    top: 0;
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.timeline-content h5 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-weight: 600;
}

.recognition-item {
    padding: 30px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.recognition-item:hover {
    transform: translateY(-3px);
}

.recognition-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.recognition-icon i {
    font-size: 1.8rem;
    color: white;
}

.recognition-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 15px 0 5px;
}

.recognition-item h6 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 10px;
}


